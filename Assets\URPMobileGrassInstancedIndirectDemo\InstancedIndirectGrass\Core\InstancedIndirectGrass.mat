%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: InstancedIndirectGrass
  m_Shader: {fileID: 4800000, guid: fd090f3b1d7b5df4f9c363d609cfa330, type: 3}
  m_ShaderKeywords: 
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2000
  stringTagMap:
    RenderType: Opaque
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseColorTexture:
        m_Texture: {fileID: 2800000, guid: edec5ef3226950841ac8090d6ac18095, type: 3}
        m_Scale: {x: 0.02, y: 0.02}
        m_Offset: {x: -0.43, y: -1.87}
    - _BaseMap:
        m_Texture: {fileID: 10309, guid: 0000000000000000f000000000000000, type: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _AlphaClip: 0
    - _Blend: 0
    - _BoundSize: 50
    - _BoundSizeX: 50
    - _BoundSizeZ: 1
    - _BumpScale: 1
    - _Cull: 2
    - _Cutoff: 0.5
    - _DrawDistance: 125
    - _DstBlend: 0
    - _EnvironmentReflections: 1
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _GrassHeight: 1
    - _GrassWidth: 1
    - _Metallic: 0
    - _OcclusionStrength: 1
    - _QueueOffset: 0
    - _RandomNormal: 0.15
    - _ReceiveShadows: 1
    - _SampleGI: 0
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Surface: 0
    - _WindAFrequency: 4
    - _WindAIntensity: 1.77
    - _WindBFrequency: 7.7
    - _WindBIntensity: 0.25
    - _WindCFrequency: 11.7
    - _WindCIntensity: 0.125
    - _WindFrequency: 4
    - _WindIntensity: 1
    - _WorkflowMode: 1
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _BoundSize: {r: 250, g: 250, b: 0, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _GroundColor: {r: 0.2195755, g: 0.46226418, b: 0.2581851, a: 1}
    - _PivotPosWS: {r: 6.2, g: 0, b: 27.1, a: 0}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - _WindADirection: {r: 0.1, g: 0.1, b: 0, a: 1}
    - _WindATiling: {r: 0.1, g: 0.1, b: 0, a: 1}
    - _WindAWrap: {r: 0.5, g: 0.5, b: 0, a: 1}
    - _WindBDirection: {r: 0.35, g: 0, b: 0, a: 1}
    - _WindBTiling: {r: 0.37, g: 3, b: 0, a: 1}
    - _WindBWrap: {r: 0.5, g: 0.5, b: 0, a: 1}
    - _WindCDirection: {r: 0.7, g: -0.1, b: 0, a: 1}
    - _WindCTiling: {r: 0.77, g: 3, b: 0, a: 1}
    - _WindCWrap: {r: 0.5, g: 0.5, b: 0, a: 1}
