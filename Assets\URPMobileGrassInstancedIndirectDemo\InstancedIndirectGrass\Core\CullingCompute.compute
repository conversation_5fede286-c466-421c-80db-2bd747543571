﻿#pragma kernel CSMain

//Matrix4x4 v = Camera.main.worldToCameraMatrix;
//Matrix4x4 p = Camera.main.projectionMatrix; //unity C# use opengl standard projection matrix
//cullingComputeShader.SetMatrix("_VPMatrix", p * v); //set from C#
float4x4 _VPMatrix;
float _MaxDrawDistance;
uint _StartOffset;
StructuredBuffer<float3> _AllInstancesPosWSBuffer; //will not change until instance count change
AppendStructuredBuffer<uint> _VisibleInstancesOnlyPosWSIDBuffer; //will set counter to 0 per frame, then fill in by this compute shader

[numthreads(64,1,1)]
void CSMain (uint3 id : SV_DispatchThreadID)
{
	//posWS -> posCS
	float4 absPosCS = abs(mul(_VPMatrix,float4(_AllInstancesPosWSBuffer[id.x + _StartOffset],1.0)));

	//do culling test in clip space, result is the same as doing test in NDC space.
	//prefer clip space here because doing culling test in clip space is faster than doing culling test in NDC, because we can skip 1 division.
	//the test is using OpenGL standard projection matrix, because all matrix from unity C# is OpenGL standard
	//if instance is inside camera frustum, and is within draw distance, we append it to _VisibleInstanceOnlyTransformBuffer
	//y test allow 50% more threshold (hardcode for grass)
	//x test allow 10% more threshold (hardcode for grass)
    if (absPosCS.z <= absPosCS.w && absPosCS.y <= absPosCS.w*1.5 && absPosCS.x <= absPosCS.w*1.1 && absPosCS.w <= _MaxDrawDistance)
		_VisibleInstancesOnlyPosWSIDBuffer.Append(id.x + _StartOffset);
}
